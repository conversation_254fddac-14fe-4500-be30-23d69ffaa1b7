import hashlib
import logging
from pathlib import Path
import platform
import socket
import sys
import threading
import subprocess
import os
import time
import psutil
import pystray
from pystray import MenuItem as item, Menu
from PIL import Image, ImageDraw
# GUI imports (optional for headless systems)
try:
    import tkinter
    from tkinter import messagebox, scrolledtext
    HAS_GUI = True
except ImportError:
    HAS_GUI = False
    logging.warning("tkinter not available - GUI dialogs will be disabled")
import shutil
import tempfile
import getpass
import sys
import os
import platform
import ctypes
# LOGO_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), "logo.png"))
BASE_DIR = getattr(sys, '_MEIPASS', os.path.dirname(os.path.abspath(__file__)))
LOGO_PATH = os.path.join(BASE_DIR, "logo.png")
def detect_linux_distribution():
    try:
        with open("/etc/os-release") as f:
            content = f.read().lower()
            if "ubuntu" in content:
                return "ubuntu"
            elif "debian" in content:
                return "debian"
            elif "fedora" in content:
                return "fedora"
            elif "arch" in content:
                return "arch"
    except:
        pass
    return "unknown"


def confirm_popup(message):
    system = platform.system()
    logging.info(f"Confirmation request on {system}: {message}")

    if system == "Linux":
        if not HAS_GUI:
            # In headless mode, log the request and auto-confirm with warning
            logging.warning("GUI not available - auto-confirming VPN request")
            logging.warning("This bypasses user consent for security reasons")
            logging.info(f"Auto-confirmed: {message}")
            return True

        # Use tkinter dialog for user confirmation
        try:
            import tkinter as tk
            from tkinter import messagebox

            root = tk.Tk()
            root.withdraw()  # Hide the main window
            root.lift()  # Bring to front
            root.attributes('-topmost', True)  # Keep on top

            result = messagebox.askyesno("CloudDeskAgent - VPN Connection", message)
            root.destroy()

            logging.info(f"User {'confirmed' if result else 'denied'}: {message}")
            return result

        except Exception as e:
            logging.error(f"Failed to show confirmation dialog: {e}")
            logging.warning("Falling back to auto-confirm due to GUI error")
            return True
    elif system == "Darwin":
        script = f'display dialog "{message}" with title "CloudDeskAgent" buttons {{"No", "Yes"}} default button "Yes"'
        result = subprocess.run(["osascript", "-e", script], capture_output=True)
        return b"button returned:Yes" in result.stdout
    elif system == "Windows":
        MB_YESNO = 0x04
        MB_ICONQUESTION = 0x20
        IDYES = 6

        result = ctypes.windll.user32.MessageBoxW(0, message, "CloudDeskAgent", MB_YESNO | MB_ICONQUESTION)
        return result == IDYES
    else:
        raise FileNotFoundError("Unkown OS")


def create_image():
    img = Image.new("RGB", (64, 64), color=(0, 128, 255))
    draw = ImageDraw.Draw(img)
    draw.rectangle((16, 16, 48, 48), fill="white")
    return img


def load_tray_icon_image():
    """Load favicon.png if present, else fallback to logo or default image."""
    # Try multiple icon sources in order of preference
    icon_paths = [
        "favicon.png",  # Favicon first
        LOGO_PATH,  # Logo path from lib
        os.path.join(os.path.dirname(__file__), "favicon.png"),  # Package favicon
        os.path.join(os.path.dirname(__file__), "..", "favicon.png"),  # Parent favicon
    ]

    for icon_path in icon_paths:
        if icon_path and os.path.exists(icon_path):
            try:
                img = Image.open(icon_path)
                if img.mode != 'RGBA':
                    img = img.convert('RGBA')
                return img
            except Exception:
                continue

    # Final fallback to generated image
    return create_image()


def update_tray_icon(icon):
    """Poll for logo changes and update the tray icon."""
    last_mtime = None
    while True:
        if LOGO_PATH and os.path.exists(LOGO_PATH):
            mtime = os.path.getmtime(LOGO_PATH)
            if last_mtime != mtime:
                try:
                    icon.icon = Image.open(LOGO_PATH)
                    last_mtime = mtime
                except Exception:
                    icon.icon = create_image()
        else:
            icon.icon = create_image()
            last_mtime = None
        time.sleep(2)  # Check every 2 seconds


def show_about():
    """Show about information"""
    print("\n🚀 CloudDeskAgent")
    print("================")
    print("Version: 1.0.0")
    print("Platform: Linux")
    print("Status: Running on http://localhost:8765")
    print("Dependencies: ✅ All OK")


def show_status():
    """Show current status"""
    print("\n📊 CloudDeskAgent Status")
    print("========================")
    print("🟢 Agent: Running")
    print("🌐 API: http://localhost:8765")
    print("🔐 Auth: Token-based")
    print("🖥️  Sessions: 0 active")


def run_tray(quit_app):
    menu = Menu(
        item("Status", show_status),
        item("About", show_about),
        item("---", None),
        item("Quit", quit_app),
    )

    icon_image = load_tray_icon_image()
    icon = pystray.Icon(
        name="clouddeskagent",
        icon=icon_image,
        title="CloudDeskAgent",
        menu=menu
    )

    threading.Thread(target=update_tray_icon, args=(icon,), daemon=True).start()
    icon.run()


def can_sudo_wgquick():
    """
    Returns True if the user can run `sudo wg-quick` without a password.
    """
    try:
        result = subprocess.run(
            ["sudo", "-n", "/usr/bin/wg-quick", "--help"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
        )
        return result.returncode == 0
    except Exception:
        return False


def show_wgquick_permission_prompt():
    username = getpass.getuser()
    session_dir = os.path.expanduser(f"~{username}/.clouddeskagent/sessions/*")
    sudoers_line = (
        f"{username} ALL=(ALL) NOPASSWD: /usr/bin/wg-quick up {session_dir}, "
        f"/usr/bin/wg-quick down {session_dir}, "
        "/usr/bin/wg-quick --help"
    )

    if not HAS_GUI:
        logging.warning("GUI not available - skipping permission prompt for headless setup")
        logging.info(f"Manual setup required: Add this line to /etc/sudoers.d/clouddeskagent: {sudoers_line}")
        return

    root = tkinter.Tk()
    root.withdraw()
    message = (
        f"This agent needs permission to manage VPNs using WireGuard.\n\n"
        f"It will add a sudoers rule restricted to:\n{session_dir}\n\n"
        f"A terminal will open where you can enter your password."
    )
    proceed = messagebox.askyesno("Allow VPN Control", message)
    root.destroy()

    if not proceed:
        messagebox.showerror(
            "Permission Denied", "The agent cannot continue without VPN privileges."
        )
        os._exit(1)

    # Create a temporary script
    with tempfile.NamedTemporaryFile("w", delete=False, suffix=".sh") as f:
        script_path = f.name
        f.write("#!/bin/sh\n")
        f.write("echo ''\n")
        f.write("echo 'This agent needs your password to configure VPN permissions.'\n")
        f.write(
            "echo 'It will allow the agent to run wg-quick for VPN tunnels without prompting again.'\n"
        )
        f.write("echo ''\n")
        f.write("echo 'You are about to authorize this change via sudo...'\n")
        f.write("echo ''\n")
        f.write(f"echo '{sudoers_line}' | sudo tee /etc/sudoers.d/clouddeskagent\n")
        f.write("echo ''\n")
        f.write("echo 'Done. Press Enter to close...'\n")
        f.write("read dummy\n")
    os.chmod(script_path, 0o755)

    terminal = detect_terminal_emulator()
    if not terminal:
        messagebox.showerror(
            "No Terminal Found", "Could not detect a terminal emulator."
        )
        os._exit(1)

    try:
        proc = subprocess.Popen([terminal, "-e", script_path])
        proc.wait()
        if proc.returncode != 0:
            messagebox.showerror(
                "Permission Denied",
                "VPN permission configuration failed or was cancelled.",
            )
            os._exit(1)
    except Exception as e:
        messagebox.showerror("Execution Error", f"Could not run permission script: {e}")
        os._exit(1)


def detect_terminal_emulator():
    for term in [
        "x-terminal-emulator",
        "gnome-terminal",
        "konsole",
        "xfce4-terminal",
        "xterm",
    ]:
        if shutil.which(term):
            return term
    return None


def wait_for_rdp(ip: str, port: int, timeout: int = 30, interval: float = 1.0) -> bool:
    """
    Waits until the RDP port at the given IP becomes reachable.

    Returns True if connection succeeds before timeout, else False.
    """
    start_time = time.time()
    attempts = 0
    max_attempts = int(timeout / interval)

    logging.info(f"Checking RDP connectivity to {ip}:{port}")

    while time.time() - start_time < timeout:
        attempts += 1
        try:
            with socket.create_connection((ip, port), timeout=3):
                logging.info(f"RDP port {ip}:{port} is reachable")
                return True
        except (ConnectionRefusedError, socket.timeout, OSError) as e:
            if attempts % 10 == 0:  # Log every 10 attempts
                elapsed = int(time.time() - start_time)
                logging.info(f"Still waiting for RDP... ({elapsed}/{timeout}s)")
            time.sleep(interval)

    logging.error(f"RDP port {ip}:{port} not reachable after {timeout} seconds")
    return False


def test_vpn_connectivity(target_ip, timeout=10):
    """
    Test if VPN connectivity is working by pinging the target IP.
    Returns True if ping succeeds, False otherwise.
    """
    logging.info(f"🔍 Testing VPN connectivity to {target_ip}")

    try:
        # Use ping to test connectivity
        result = subprocess.run(
            ["ping", "-c", "3", "-W", str(timeout), target_ip],
            capture_output=True,
            text=True,
            timeout=timeout + 5
        )

        if result.returncode == 0:
            logging.info(f"✅ VPN connectivity test passed - {target_ip} is reachable")
            return True
        else:
            logging.error(f"❌ VPN connectivity test failed - {target_ip} is not reachable")
            logging.error(f"   Ping output: {result.stdout.strip()}")

            # Additional diagnostics
            logging.info("🔧 VPN Diagnostics:")
            try:
                # Check WireGuard status
                wg_result = subprocess.run(["sudo", "wg", "show"], capture_output=True, text=True, timeout=5)
                if wg_result.returncode == 0:
                    logging.info(f"   WireGuard status: {wg_result.stdout.strip()}")
                else:
                    logging.error(f"   Could not get WireGuard status: {wg_result.stderr.strip()}")

                # Check routing
                route_result = subprocess.run(["ip", "route", "show"], capture_output=True, text=True, timeout=5)
                if route_result.returncode == 0:
                    vpn_routes = [line for line in route_result.stdout.split('\n') if '10.' in line or 'session' in line]
                    logging.info(f"   VPN routes: {vpn_routes}")

            except Exception as e:
                logging.error(f"   Could not run diagnostics: {e}")

            return False

    except subprocess.TimeoutExpired:
        logging.error(f"❌ VPN connectivity test timed out after {timeout} seconds")
        return False
    except Exception as e:
        logging.error(f"❌ VPN connectivity test failed with error: {e}")
        return False


class StatusWindow:
    def __init__(self, title="CloudDeskAgent Status"):
        self.root = tkinter.Tk()
        self.root.title(title)
        self.root.geometry("400x200")
        self.text = scrolledtext.ScrolledText(self.root, state="disabled", wrap="word")
        self.text.pack(expand=True, fill="both")
        self.root.protocol("WM_DELETE_WINDOW", self.disable_close)
        self._thread = threading.Thread(target=self.root.mainloop, daemon=True)
        self._thread.start()

    def log(self, message):
        self.text.config(state="normal")
        self.text.insert("end", message + "\n")
        self.text.see("end")
        self.text.config(state="disabled")
        self.root.update_idletasks()

    def close(self):
        self.root.quit()
        self.root.destroy()

    def disable_close(self):
        pass  # Disables window close button during session


def generate_remmina_profile(session_dir, session_id, rdp_target_ip, rdp_target_port):
    profile_path = os.path.join(session_dir, f"{session_id}.remmina")
    with open(profile_path, "w") as f:
        f.write(
            """[remmina]
protocol=RDP
server={}:{}
username=
name=DirectRDP
hidden=true
""".format(
                rdp_target_ip, rdp_target_port
            )
        )
    return profile_path


def log_vpn_output(proc, sid):
    for line in proc.stdout:
        logging.info(f"[wg-quick {sid}] {line.strip()}")


def log_remmina_output(proc, sid):
    for line in proc.stdout:
        logging.info(f"[remmina {sid}] {line.strip()}")
        if "(rco_on_disconnect) - Disconnected" in line:
            proc.kill()
        if "(rco_on_disconnect) - Could not disconnect" in line:
            proc.kill()


def ask_remmina_install_permission():
    """
    Shows a tkinter popup asking the user for permission to install Remmina.
    Returns True if user agrees, False otherwise.
    """
    if not HAS_GUI:
        logging.warning("GUI not available - skipping Remmina installation prompt for headless setup")
        logging.info("Manual setup required: Install remmina with your package manager (e.g., apt install remmina)")
        return

    root = tkinter.Tk()
    root.withdraw()  # Hide the main window
    result = messagebox.askyesno(
        title="Install Remmina",
        message="Remmina is not installed.\n\nRemmina is a secure remote desktop (RDP) client used by this agent.\n\nWould you like to install it now?",
    )
    root.destroy()
    if not result:
        messagebox.showerror(
            "Permission Denied",
            "The agent cannot continue without the remmina RDP client.",
        )
        os._exit(1)

    distro = detect_linux_distribution()
    if distro in ["debian", "ubuntu"]:
        remmina_installer = "sudo apt install -y remmina"
    elif distro == "fedora":
        remmina_installer = "sudo dnf install -y remmina"
    elif distro == "arch":
        remmina_installer = "sudo pacman -S --noconfirm remmina"
    else:
        remmina_installer = "flatpak install -y flathub org.remmina.Remmina"

    # Create a temporary script
    with tempfile.NamedTemporaryFile("w", delete=False, suffix=".sh") as f:
        script_path = f.name
        f.write("#!/bin/sh\n")
        f.write("echo ''\n")
        f.write("echo 'This agent needs your password to install remmina.'\n")
        f.write(f"echo '{remmina_installer}'\n")
        f.write(f"{remmina_installer}\n")
        f.write("echo ''\n")
        f.write("echo 'Done. Press Enter to close...'\n")
        f.write("read dummy\n")
    os.chmod(script_path, 0o755)

    terminal = detect_terminal_emulator()
    if not terminal:
        messagebox.showerror(
            "No Terminal Found", "Could not detect a terminal emulator."
        )
        os._exit(1)

    try:
        proc = subprocess.Popen([terminal, "-e", script_path])
        proc.wait()
        if proc.returncode != 0:
            messagebox.showerror(
                "Permission Denied", "remmina installation failed or was cancelled."
            )
            os._exit(1)
    except Exception as e:
        messagebox.showerror(
            "Execution Error", f"Could not run remmina installer script: {e}"
        )
        os._exit(1)


def is_microsoft_remote_desktop_installed():
    """
    Checks if Microsoft Remote Desktop is installed on macOS.
    Returns True if installed, False otherwise.
    """
    expected_paths = [
        "/Applications/Windows App.app",
        os.path.expanduser("~/Applications/Windows App.app")
    ]

    return any(os.path.exists(path) for path in expected_paths)

def open_microsoft_rdp_download_page():
    subprocess.run([
        "open",
        "macappstore://apps.apple.com/app/microsoft-remote-desktop/id1295203466"
    ])

def macos_ask_yes_no(title: str, message: str) -> bool:
    """Display a native macOS dialog with Yes/No buttons."""
    message = message.replace('"', '\\"').replace("\n", "\\n")
    title = title.replace('"', '\\"')
    script = f'display dialog "{message}" with title "{title}" buttons {{"No", "Yes"}} default button "Yes"'
    result = subprocess.run(["osascript", "-e", script], capture_output=True)
    return b"button returned:Yes" in result.stdout

def macos_ask_ok_cancel(title: str, message: str) -> bool:
    """Display a native macOS dialog with OK/Cancel buttons."""
    message = message.replace('"', '\\"').replace("\n", "\\n")
    title = title.replace('"', '\\"')
    script = f'display dialog "{message}" with title "{title}" buttons {{"Cancel", "OK"}} default button "OK"'
    result = subprocess.run(["osascript", "-e", script], capture_output=True)
    return b"button returned:OK" in result.stdout

def prompt_to_install_rdp() -> bool:
    if not macos_ask_yes_no(
        "CloudDeskAgent — Install RDP",
        "Microsoft Remote Desktop is not installed.\n\n"
        "Without this software the agent cannot continue.\n\n"
        "Would you like to open the App Store to install it?"
    ):
        print("Cannot continue without the Microsoft Remote Desktop software.", file=sys.stderr)
        return False

    open_microsoft_rdp_download_page()

    while True:
        if macos_ask_ok_cancel(
            "CloudDeskAgent — Confirm",
            "Press OK after you installed the Microsoft Remote Desktop using the Apple App Store."
        ):
            if is_microsoft_remote_desktop_installed():
                return True
            else:
                retry = macos_ask_yes_no(
                    "CloudDeskAgent — Confirm",
                    "The Microsoft Remote Desktop software was not detected.\n\n"
                    "This software is required for using the CloudDeskAgent.\n\n"
                    "Do you want to retry installing the Microsoft Remote Desktop software?\n\n"
                    "Press Yes to retry.\nPress No to exit the CloudDeskAgent."
                )
                if retry:
                    open_microsoft_rdp_download_page()
                else:
                    print("Cannot continue without the Microsoft Remote Desktop software.", file=sys.stderr)
                    return False
        else:
            if is_microsoft_remote_desktop_installed():
                return True
            else:
                print("Cannot continue without the Microsoft Remote Desktop software.", file=sys.stderr)
                return False

def install_package_with_prompt(package_name, description):
    """
    Prompts user to install a package and attempts automatic installation.
    Returns True if successful, False if user declined or installation failed.
    """
    print(f"\n🔧 Missing Dependency: {package_name}")
    print(f"   {description}")
    print(f"   This is required for the CloudDeskAgent to work properly.")

    if not HAS_GUI:
        # Headless mode - try to install automatically
        print(f"\n⚡ Attempting to install {package_name} automatically...")
        return auto_install_package(package_name)
    else:
        # GUI mode - ask user first
        response = input(f"\n❓ Would you like to install {package_name} now? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            return auto_install_package(package_name)
        else:
            print(f"❌ Cannot continue without {package_name}. Please install it manually and restart the agent.")
            return False


def auto_install_package(package_name):
    """
    Automatically installs a package using the appropriate package manager.
    Returns True if successful, False otherwise.
    """
    distro = detect_linux_distribution()

    # Map package names to different distros if needed
    package_map = {
        "remmina": {
            "ubuntu": "remmina",
            "debian": "remmina",
            "fedora": "remmina",
            "arch": "remmina",
            "unknown": "remmina"
        },
        "wireguard-tools": {
            "ubuntu": "wireguard-tools",
            "debian": "wireguard-tools",
            "fedora": "wireguard-tools",
            "arch": "wireguard-tools",
            "unknown": "wireguard-tools"
        }
    }

    actual_package = package_map.get(package_name, {}).get(distro, package_name)

    # Choose the right package manager
    if distro in ["ubuntu", "debian"]:
        cmd = ["sudo", "apt", "update", "&&", "sudo", "apt", "install", "-y", actual_package]
        cmd_str = f"sudo apt update && sudo apt install -y {actual_package}"
    elif distro == "fedora":
        cmd_str = f"sudo dnf install -y {actual_package}"
    elif distro == "arch":
        cmd_str = f"sudo pacman -S --noconfirm {actual_package}"
    else:
        print(f"⚠️  Unknown distribution. Please install {actual_package} manually using your package manager.")
        return False

    print(f"🔄 Running: {cmd_str}")
    try:
        result = subprocess.run(cmd_str, shell=True, check=True)
        print(f"✅ Successfully installed {package_name}!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package_name}. Error: {e}")
        print(f"💡 Please install manually: {cmd_str}")
        return False


def check_dependencies():
    if platform.system() == "Linux":
        print("\n🐧 CloudDeskAgent - Linux Setup")
        print("=" * 40)

        # Check WireGuard
        if not shutil.which("wg-quick"):
            print("❌ WireGuard not found")
            if not install_package_with_prompt("wireguard-tools", "VPN tunnel software for secure connections"):
                os._exit(1)
        else:
            print("✅ WireGuard found")

        # Check WireGuard permissions
        if not can_sudo_wgquick():
            print("⚠️  WireGuard permissions need setup")
            show_wgquick_permission_prompt()
        else:
            print("✅ WireGuard permissions OK")

        # Check Remmina
        if not shutil.which("remmina"):
            print("❌ Remmina (RDP client) not found")
            if not install_package_with_prompt("remmina", "Remote desktop client for connecting to Windows machines"):
                os._exit(1)
        else:
            print("✅ Remmina found")

        print("\n🎉 All dependencies are ready!")
        print("   Starting CloudDeskAgent...")
    elif platform.system() == "Darwin":
        logging.info("Detected OS: MacOSX")
        if not is_microsoft_remote_desktop_installed():
            logging.info("Remote Desktop Software is not installed")
            if not prompt_to_install_rdp():
                logging.info("The user did not install the Remote Desktop Software")
                os._exit(1)
        logging.info("The Remote Desktop Software is installed and available")
        
        if not has_sudo_permission_for_agent():
            logging.info("The sudo permissions for wg-quick-py.py are not in place")
            if not request_sudo_permission_for_agent():
                logging.info("The user did not put the sudo permissions in place")
                os._exit(1)
            else:
                if not has_sudo_permission_for_agent():
                    logging.info("The user did not put the sudo permissions in place")
                    os._exit(1)
        logging.info("Sudo permissions for the wg-quick-py.py are in place.")
    elif platform.system() == "Windows":
        logging.info("Detected OS: Windows")

        # Check mstsc.exe (RDP Client) presence
        mstsc_path = os.path.join(os.environ["SystemRoot"], "System32", "mstsc.exe")
        if not os.path.exists(mstsc_path):
            logging.error("Remote Desktop Client (mstsc.exe) not found.")
            logging.error("Error: Remote Desktop Client (mstsc.exe) is missing.")
            os._exit(1)
        else:
            logging.info("Remote Desktop Client (mstsc.exe) is installed and available.")

    else:
        logging.error("Unknown unsupported OS")
        os._exit(1)

def request_sudo_permission_for_agent() -> bool:
    user = getpass.getuser()
    agent_script = Path(__file__).resolve().parent / "wg-quick-py.py"
    interpreter = sys.executable

    sudoers_command_line = f"{user} ALL=(ALL) NOPASSWD: {interpreter} {agent_script} *"
    sudoers_defaults_line = f'Defaults!{interpreter} env_keep += "PATH, PYTHONPATH"'
    completion_flag = f"/tmp/clouddeskagent-sudo-done-{os.getpid()}"

    # --- 1. Ask user using macOS native dialog ---
    dialog = (
        'display dialog "The CloudDeskAgent needs permission to set up secure tunnels using WireGuard.\\n\\n'
        'macOS will ask for your password in the next step.\\n\\n'
        'This is required so tunnels can be established automatically.\\n\\n'
        'Do you want to continue?" '
        'with title "CloudDeskAgent Setup" buttons {"No", "Yes"} default button "Yes"'
    )

    result = subprocess.run(["osascript", "-e", dialog], capture_output=True)
    if b"button returned:Yes" not in result.stdout:
        print("User cancelled sudo setup.")
        return False

    # --- 2. Bring Terminal forward (reliably) ---
    subprocess.run(["osascript", "-e", 'tell application "Finder" to activate'])
    time.sleep(0.2)
    subprocess.run(["osascript", "-e", 'tell application "Terminal" to activate'])

    script_path = None
    sh_path = None

    try:
        # --- 3. Shell script that performs sudoers setup ---
        with tempfile.NamedTemporaryFile("w", suffix=".sh", delete=False) as sh_file:
            sh_path = sh_file.name
            sh_file.write(f"""#!/bin/bash
clear
echo ""
echo "============================"
echo " CloudDeskAgent Permission Setup"
echo "============================"
echo "The CloudDeskAgent needs permission to set up secure tunnels."
echo "You are now asked to enter your macOS password."
echo "This is required so VPN tunnels can be established without manual intervention."
echo ""
echo '{sudoers_command_line}' | sudo tee /etc/sudoers.d/clouddeskagent > /dev/null
echo '{sudoers_defaults_line}' | sudo tee -a /etc/sudoers.d/clouddeskagent > /dev/null
sudo visudo -cf /etc/sudoers.d/clouddeskagent
sudo chmod 440 /etc/sudoers.d/clouddeskagent
echo ""
echo "✅ Permission configured. You may close this window."
touch {completion_flag}
osascript -e 'tell application "Terminal" to close front window' &>/dev/null
""")

        os.chmod(sh_path, 0o755)

        # --- 4. AppleScript to launch the shell script ---
        escaped_path = sh_path.replace("'", "'\\''")
        with tempfile.NamedTemporaryFile("w", suffix=".applescript", delete=False) as script_file:
            script_path = script_file.name
            script_file.write(f"""tell application "Terminal"
    do script "bash -c '{escaped_path}'"
    activate
end tell""")

        subprocess.run(["osascript", script_path], check=True)

        # --- 5. Wait for shell script to finish ---
        for _ in range(120):  # 60s max
            if os.path.exists(completion_flag):
                break
            time.sleep(0.5)
        else:
            print("Timed out waiting for Terminal script to complete.")
            return False

        return True

    except subprocess.CalledProcessError as err:
        print("Failed to set sudo permissions:", err)
        return False
    except Exception as err:
        print("Unexpected error:", err)
        return False
    finally:
        for path in (script_path, sh_path, completion_flag):
            try:
                if path and os.path.exists(path):
                    os.remove(path)
            except Exception:
                pass

def has_sudo_permission_for_agent():
    import getpass
    import sys
    from pathlib import Path

    user = getpass.getuser()
    agent_script = Path(__file__).resolve().parent / "wg-quick-py.py"
    interpreter = sys.executable

    try:
        result = subprocess.run(
            ["sudo", "-l", "-n", "-U", user],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        if result.returncode == 0 and f"{interpreter} {agent_script}" in result.stdout:
            return True
    except Exception as e:
        print("Error checking sudo permissions:", e)

    return False

def start_rdp_session(rdp_app: str, rdp_file: str, on_exit: callable = None) -> int:
    # Snapshot of running Windows App PIDs
    existing_pids = {
        p.pid for p in psutil.process_iter(["pid", "name"])
        if p.info["name"] == rdp_app
    }

    # Launch new instance with the RDP file
    subprocess.run(["open", "-n", "-a", rdp_app, rdp_file])
    time.sleep(1.5)  # Allow the process to start and appear in psutil

    # Identify the new process
    new_pids = [
        p for p in psutil.process_iter(["pid", "name"])
        if p.info["name"] == rdp_app and p.pid not in existing_pids
    ]

    if not new_pids:
        raise RuntimeError("Failed to detect newly launched Windows App process.")

    proc = new_pids[0]
    pid = proc.pid
    print(f"[RDP] Launched new session with PID {pid}")

    # Background monitor thread
    def monitor():
        try:
            proc.wait()
        except Exception:
            pass
        print(f"[RDP] Session with PID {pid} has exited.")
        if on_exit:
            on_exit()

    thread = threading.Thread(target=monitor, daemon=True)
    thread.start()

    return pid


def is_wintun_driver_installed():
    try:
        result = subprocess.run(
            ["reg", "query", r"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\Wintun"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        return result.returncode == 0
    except Exception as e:
        logging.error(f"Failed to check Wintun driver installation: {e}")
        return False


def add_shortcut_to_startup(shortcut_name="CloudDeskAgent"):
    startup = os.path.join(os.environ["APPDATA"], r"Microsoft\Windows\Start Menu\Programs\Startup")
    target = sys.executable
    script = os.path.abspath(__file__)
    lnk_path = os.path.join(startup, f"{shortcut_name}.lnk")

    shell = Dispatch('WScript.Shell')
    shortcut = shell.CreateShortCut(lnk_path)
    if target.lower().endswith("python.exe") or target.lower().endswith("pythonw.exe"):
        shortcut.Targetpath = target
        shortcut.Arguments = f'"{script}"'
    else:
        shortcut.Targetpath = target
    shortcut.WorkingDirectory = os.path.dirname(script)
    shortcut.save()
    logging.info(f"Shortcut created at {lnk_path}")


def get_logo_md5():
    """Compute the MD5 hash of the logo file, or return None if missing."""
    if not LOGO_PATH or not os.path.exists(LOGO_PATH):
        return None
    hash_md5 = hashlib.md5()
    with open(LOGO_PATH, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()
