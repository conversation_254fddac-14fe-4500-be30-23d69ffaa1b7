import os
import subprocess
import signal
import time
import logging
import argparse
import re
from pathlib import Path
import sys
import json

SCRIPT_DIR = Path(__file__).resolve().parent
WIREGUARD_GO = SCRIPT_DIR / "../bin/osx/wireguard"
WG_BIN = SCRIPT_DIR / "../bin/osx/wg"
CONFIG_JSON = SCRIPT_DIR / "../config.json"
SESSION_DIR = Path.home() / ".clouddeskagent" / "sessions"
SESSION_DIR.mkdir(parents=True, exist_ok=True)

logging.basicConfig(level=logging.INFO)

def parse_wg_config(json_str):
    data = json.loads(json_str)
    interface = data.get("interface", {})
    peer = data.get("peer", {})
    return {
        "address": interface.get("address"),
        "public_key": peer.get("publickey"),
        "endpoint": peer.get("endpoint"),
        "allowed_ips": peer.get("allowedips", "")
    }

def load_private_key():
    with open(CONFIG_JSON) as f:
        config = json.load(f)
    return config["private_key"]

def find_available_utun():
    existing = subprocess.run(["ifconfig"], capture_output=True, text=True).stdout
    used = set(re.findall(r"utun(\d+)", existing))
    for i in range(100):
        if str(i) not in used:
            return f"utun{i}"
    raise RuntimeError("No available utun device found")

class VPNTunnel:
    def __init__(self, config_json_str, session_id):
        self.session_id = session_id
        self.config = parse_wg_config(config_json_str)
        self.interface = find_available_utun()
        self.address = self.config["address"]
        self.allowed_ips = self.config["allowed_ips"]
        self.private_key = load_private_key()
        self.wg_proc = None
        self.session_conf = SESSION_DIR / f"{session_id}.conf"

    def _wait_for_interface(self, interface, timeout=5):
        logging.info(f"Waiting for interface {interface} to be created...")
        start_time = time.time()
        while time.time() - start_time < timeout:
            result = subprocess.run(["ifconfig"], capture_output=True, text=True)
            if interface in result.stdout:
                logging.info(f"Interface {interface} is now available.")
                return
            time.sleep(0.1)
        raise RuntimeError(f"Timeout: Interface {interface} not found.")

    def start(self):
        signal.signal(signal.SIGTERM, self.handle_sigterm)
        logging.info(f"Starting wireguard-go with interface {self.interface}")
        self.wg_proc = subprocess.Popen([str(WIREGUARD_GO), "-f", self.interface])
        self._wait_for_interface(self.interface)

        ip = self.address.split('/')[0]
        logging.info(f"Bringing up interface {self.interface} with IP {ip}")
        subprocess.run(["ifconfig", self.interface, "inet", ip, ip, "up"], check=True)

        logging.info("Applying WireGuard config with wg")
        with open(self.session_conf, 'w') as f:
            f.write("[Interface]\n")
            f.write(f"PrivateKey = {self.private_key}\n")
            f.write("\n[Peer]\n")
            f.write(f"PublicKey = {self.config['public_key']}\n")
            f.write(f"Endpoint = {self.config['endpoint']}\n")
            f.write(f"AllowedIPs = {self.allowed_ips}\n")

        subprocess.run([str(WG_BIN), "setconf", self.interface, str(self.session_conf)], check=True)

        for net in self.allowed_ips.split(','):
            net = net.strip()
            logging.info(f"Adding route: {net} via {ip}")
            subprocess.run(["route", "-n", "add", "-net", net, ip], check=True)

        logging.info("VPN tunnel established. Waiting on wireguard-go process...")
        self.wg_proc.wait()

    def handle_sigterm(self, signum, frame):
        logging.info("Received SIGTERM. Cleaning up...")
        self.cleanup()
        sys.exit(0)

    def cleanup(self):
        ip = self.address.split('/')[0]
        logging.info("Tearing down tunnel")
        for net in self.allowed_ips.split(','):
            try:
                subprocess.run(["route", "-n", "delete", "-net", net.strip()], check=True)
            except subprocess.CalledProcessError:
                pass
        if self.wg_proc and self.wg_proc.poll() is None:
            self.wg_proc.kill()
        if self.session_conf.exists():
            self.session_conf.unlink()
        logging.info("VPN tunnel closed.")

def main():
    parser = argparse.ArgumentParser(description="WireGuard macOS tunnel manager")
    parser.add_argument("--config", type=str, required=True, help="WireGuard config as JSON string")
    parser.add_argument("--session-id", type=str, required=True, help="Unique session ID")
    args = parser.parse_args()

    tunnel = VPNTunnel(args.config, args.session_id)
    try:
        tunnel.start()
    except Exception as e:
        logging.error(f"Error occurred: {e}")
    finally:
        tunnel.cleanup()

if __name__ == "__main__":
    if os.geteuid() != 0:
        print("This script must be run as root.", file=sys.stderr)
        sys.exit(1)
    main()
