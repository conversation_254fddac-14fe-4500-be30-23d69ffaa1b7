import ctypes
import logging
import json
import os
import subprocess
import sys
import time
from pathlib import Path
import threading
import win32serviceutil
import win32service
import win32event
import win32pipe
import win32file
import win32security
import win32con
import pywintypes

# Logging to file for debugging service output

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
CREATE_NO_WINDOW = getattr(subprocess, "CREATE_NO_WINDOW", 0)

LOG_PATH = os.path.join(SCRIPT_DIR, "logs.txt")
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    # filename=LOG_PATH,
    # filemode="a",
    handlers=[logging.StreamHandler()],

)

SCRIPT_DIR = Path(__file__).resolve().parent
WIREGUARD_EXE = SCRIPT_DIR / "../bin/windows/wireguard.exe"
CONFIG_JSON = SCRIPT_DIR / "../config.json"
SESSION_DIR = Path.home() / ".clouddeskagent" / "sessions"
SESSION_DIR.mkdir(parents=True, exist_ok=True)

def load_private_key():
    with open(CONFIG_JSON) as f:
        config = json.load(f)
    return config["private_key"]

class VPNTunnel:
    def __init__(self, config_json_str, session_id):
        self.session_id = session_id
        data = json.loads(config_json_str)
        interface = data.get("interface", {})
        peer = data.get("peer", {})
        self.address = interface.get("address")
        self.public_key = peer.get("publickey")
        self.endpoint = peer.get("endpoint")
        self.allowed_ips = peer.get("allowedips", "")
        self.session_conf = SESSION_DIR / f"{session_id}.conf"

    def create_config_file(self):
        logging.info(f"Creating WireGuard config file: {self.session_conf}")
        private_key = load_private_key()
        with open(self.session_conf, 'w') as f:
            f.write("[Interface]\n")
            f.write(f"PrivateKey = {private_key}\n")
            f.write(f"Address = {self.address}\n\n")
            f.write("[Peer]\n")
            f.write(f"PublicKey = {self.public_key}\n")
            f.write(f"Endpoint = {self.endpoint}\n")
            f.write(f"AllowedIPs = {self.allowed_ips}\n")


    def install_tunnel_service(self):
        params = [str(WIREGUARD_EXE), '/installtunnelservice', str(self.session_conf.resolve())]
        logging.info(f"Installing tunnel service with: {' '.join(params)}")
        ret = subprocess.run(params)
        if ret.returncode != 0:
            raise RuntimeError(f"Failed to install tunnel service. wireguard.exe returned {ret.returncode}")

    def uninstall_tunnel_service(self):
        params = [str(WIREGUARD_EXE), '/uninstalltunnelservice', self.session_conf.stem]
        logging.info(f"Uninstalling tunnel service with: {' '.join(params)}")
        ret = subprocess.run(params)
        if ret.returncode != 0:
            raise RuntimeError(f"Failed to uninstall tunnel service. wireguard.exe returned {ret.returncode}")


    def cleanup(self):
        self.uninstall_tunnel_service()
        if self.session_conf.exists():
            self.session_conf.unlink()
        logging.info("Tunnel cleaned up.")

class VPNAdminService(win32serviceutil.ServiceFramework):
    _svc_name_ = "VPNAdminService"
    _svc_display_name_ = "VPN Admin Tunnel Service"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.keep_running = True

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        self.keep_running = False
        win32event.SetEvent(self.hWaitStop)

    def SvcDoRun(self):
        logging.info("VPNAdminService started.")
        self.keep_running = True  # <--- make sure this is here
        t = threading.Thread(target=self.pipe_server, daemon=True)
        logging.info("starting the thread")
        t.start()
        win32event.WaitForSingleObject(self.hWaitStop, win32event.INFINITE)
        logging.info("VPNAdminService stopped.")

    def get_pipe_security_attributes(self):
        sa = win32security.SECURITY_ATTRIBUTES()
        sd = win32security.SECURITY_DESCRIPTOR()
        sd.Initialize()
        everyone, _, _ = win32security.LookupAccountName("", "Everyone")
        acl = win32security.ACL()
        acl.AddAccessAllowedAce(win32con.ACL_REVISION, win32con.GENERIC_ALL, everyone)
        sd.SetSecurityDescriptorDacl(1, acl, 0)
        sa.SECURITY_DESCRIPTOR = sd
        return sa

    def pipe_server(self):
        import traceback
        pipe_name = r'\\.\pipe\Global\VPNAdminPipe'
        try:
            logging.info("Pipe server thread started.")
            while self.keep_running:
                logging.info("About to create named pipe...")
                pipe = win32pipe.CreateNamedPipe(
                    pipe_name,
                    win32pipe.PIPE_ACCESS_DUPLEX,
                    win32pipe.PIPE_TYPE_MESSAGE | win32pipe.PIPE_READMODE_MESSAGE | win32pipe.PIPE_WAIT,
                    4, 65536, 65536,
                    0,
                    self.get_pipe_security_attributes()
                )
                logging.info("Pipe created, waiting for client connection...")
                try:
                    win32pipe.ConnectNamedPipe(pipe, None)
                    logging.info("Client connected to pipe.")
                    while self.keep_running:
                        try:
                            resp = win32file.ReadFile(pipe, 4096)
                            if resp:
                                command = resp[1].decode('utf-8').strip()
                                logging.info(f"Received command: {command}")
                                self.handle_command(command)
                        except pywintypes.error as e:
                            if e.winerror == 109:
                                logging.info("Client disconnected.")
                                break
                            else:
                                logging.error(f"Pipe read error: {e}")
                                break
                except Exception as e:
                    logging.error(f"Pipe connection error: {e}")
                finally:
                    win32file.CloseHandle(pipe)
                    logging.info("Pipe closed, waiting for next client...")
        except Exception as ex:
            logging.error("Exception in pipe_server: %s", traceback.format_exc())

    def handle_command(self, command):
        try:
            data = json.loads(command)
            cmd = data.get("cmd")
            if cmd == "install":
                wg_config = data["config"]    # This is a dict
                session_id = data["session_id"]
                tunnel = VPNTunnel(json.dumps(wg_config), session_id)
                tunnel.create_config_file()
                tunnel.install_tunnel_service()
                logging.info(f"Tunnel for session {session_id} installed.")
            elif cmd == "uninstall":
                session_id = data["session_id"]
                conf_file = SESSION_DIR / f"{session_id}.conf"
                if conf_file.exists():
                    params = f'/uninstalltunnelservice {conf_file.stem}'
                    logging.info(f"Uninstalling tunnel service for {conf_file.stem}...")
                    ret = ctypes.windll.shell32.ShellExecuteW(None, "runas", str(WIREGUARD_EXE), params, None, 1)
                    if ret > 32:
                        conf_file.unlink()
                        logging.info(f"Tunnel service {conf_file.stem} uninstalled.")
                    else:
                        logging.error(f"Failed to uninstall tunnel service. ShellExecuteW returned {ret}")
                else:
                    logging.warning(f"Config file {conf_file} not found for uninstall.")
            else:
                logging.warning(f"Unknown command received: {cmd}")
        except Exception as e:
            logging.error(f"Error handling command: {e}", exc_info=True)


import subprocess, winreg, logging

CREATE_NO_WINDOW = getattr(subprocess, "CREATE_NO_WINDOW", 0)

def set_service_autostart(service_name: str, delayed: bool = True) -> bool:
    """
    Mark the Windows service to start automatically at boot.
    Call this while running as admin. No console windows will flash.
    """
    if not _is_admin():
        logging.debug("Skipping set_service_autostart: not elevated (will be set during --install-service).")
        return True

    try:
        # set StartType = AUTO_START
        r = subprocess.run(
            ["sc.exe", "config", service_name, "start=", "auto"],
            capture_output=True, text=True, creationflags=CREATE_NO_WINDOW
        )
        if r.returncode != 0:
            logging.error(f"sc config failed ({r.returncode}): {r.stderr.strip() or r.stdout.strip()}")
            return False

        # optional but recommended: delayed auto-start
        if delayed:
            try:
                with winreg.OpenKey(
                    winreg.HKEY_LOCAL_MACHINE,
                    rf"SYSTEM\CurrentControlSet\Services\{service_name}",
                    0, winreg.KEY_SET_VALUE
                ) as k:
                    winreg.SetValueEx(k, "DelayedAutoStart", 0, winreg.REG_DWORD, 1)
            except Exception as e:
                logging.warning(f"Could not set DelayedAutoStart: {e}")

        logging.info(f"{service_name} set to auto-start (delayed={bool(delayed)}).")
        return True
    except Exception as e:
        logging.exception(f"set_service_autostart failed: {e}")
        return False


def ensure_vpn_service_running():
    import time
    service_name = "VPNAdminService"
    MAX_INSTALL_WAIT = 90   # seconds
    INSTALL_POLL = 1        # seconds

    try:
        status = win32serviceutil.QueryServiceStatus(service_name)[1]
        if status == win32service.SERVICE_RUNNING:
            logging.info(f"{service_name} is already running.")
            return True
        elif status == win32service.SERVICE_STOPPED:
            logging.info(f"{service_name} exists but is stopped. Attempting to start...")
            # Request UAC elevation to start service
            ret = ctypes.windll.shell32.ShellExecuteW(
                None, "runas", "sc.exe", f"start {service_name}", None, 1
            )
            if ret <= 32:
                logging.error(f"Failed to start {service_name} via UAC. Return code: {ret}")
                return False

            # Wait for service to start
            for i in range(MAX_INSTALL_WAIT):
                time.sleep(INSTALL_POLL)
                status = win32serviceutil.QueryServiceStatus(service_name)[1]
                if status == win32service.SERVICE_RUNNING:
                    logging.info(f"{service_name} started successfully.")
                    set_service_autostart(service_name)
                    return True
            logging.error(f"{service_name} failed to start in time.")
            return False

        else:
            logging.error(f"{service_name} is in unexpected state: {status}")
            return False

    except pywintypes.error as e:
        if e.winerror == 1060:  # Service does not exist
            logging.info(f"{service_name} is not installed. Attempting installation via UAC...")

            # Relaunch THIS EXE elevated with an internal flag (no PowerShell, no script path)
            rc = ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, "--install-service", None, 1)

            logging.info(f"elevated installer ShellExecuteW returned: {rc}")
            if rc <= 32:
                logging.error("Failed to launch service installation elevated.")
                return False

            # Wait for service to be created and start running
            deadline = time.time() + MAX_INSTALL_WAIT
            while time.time() < deadline:
                try:
                    status = win32serviceutil.QueryServiceStatus(service_name)[1]
                    if status == win32service.SERVICE_RUNNING:
                        logging.info(f"{service_name} installed and running.")
                        set_service_autostart(service_name)
                        return True
                    if status == win32service.SERVICE_STOPPED:
                        logging.info(f"{service_name} installed but stopped. Starting...")
                        r = subprocess.run(["sc.exe", "start", service_name],
                                        capture_output=True, text=True,
                                        creationflags=CREATE_NO_WINDOW)
                        if r.returncode == 0:
                            # brief extra wait for RUNNING
                            inner_deadline = time.time() + MAX_INSTALL_WAIT
                            while time.time() < inner_deadline:
                                time.sleep(INSTALL_POLL)
                                if win32serviceutil.QueryServiceStatus(service_name)[1] == win32service.SERVICE_RUNNING:
                                    logging.info(f"{service_name} started.")
                                    set_service_autostart(service_name)
                                    return True
                        else:
                            logging.error(f"sc start after install failed: {r.stderr or r.stdout}")
                            return False
                except pywintypes.error as e2:
                    if e2.winerror == 1060:
                        logging.info("Waiting for service registration...")
                        time.sleep(INSTALL_POLL)
                        continue
                    logging.error(f"Error querying service status during install: {e2}")
                    return False

            logging.error("Service installation/start timed out.")
            return False
        else:
            logging.error(f"Error querying service status: {e}")
            return False

def _is_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['install', 'start', 'stop', 'remove']:
        win32serviceutil.HandleCommandLine(VPNAdminService)
    else:
        print("Usage: python script.py install|start|stop|remove")
