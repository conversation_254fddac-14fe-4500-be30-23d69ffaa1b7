#!/bin/bash
# CloudDeskAgent Universal Linux Installer
# Version: 1.0.0
# Description: Installs CloudDeskAgent with all dependencies across Linux distros

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# Configuration
readonly SCRIPT_NAME="CloudDeskAgent Installer"
readonly VERSION="1.0.0"
# TODO: Update this repository when changing GitHub location
# Current repository: HaSanAlkholy/agent
# Format: USERNAME/REPOSITORY
readonly GITHUB_REPO="HaSanAlkholy/agent"
# AppImage name will be set based on architecture
APPIMAGE_NAME=""
readonly LOG_FILE="$HOME/.clouddeskagent-install.log"
readonly INSTALL_DIR="$HOME/.local/bin"
readonly DESKTOP_DIR="$HOME/.local/share/applications"
readonly AUTOSTART_DIR="$HOME/.config/autostart"
readonly CONFIG_DIR="$HOME/.clouddeskagent"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# Logging functions
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

log_debug() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [DEBUG] $1" >> "$LOG_FILE"
}

log_command() {
    local cmd="$1"
    local description="$2"
    log_debug "Executing: $cmd"
    log_info "$description"

    if eval "$cmd" >> "$LOG_FILE" 2>&1; then
        log_debug "Command succeeded: $cmd"
        return 0
    else
        local exit_code=$?
        log_debug "Command failed with exit code $exit_code: $cmd"
        log_error "$description failed"
        return $exit_code
    fi
}

# Error handling
cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Installation failed with exit code $exit_code"
        log_info "Check the log file: $LOG_FILE"
        log_info "For support, visit: https://github.com/$GITHUB_REPO/issues"
    fi
}

trap cleanup EXIT

# Utility functions
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

is_root() {
    [ "$EUID" -eq 0 ]
}

get_architecture() {
    case $(uname -m) in
        x86_64) echo "x86_64" ;;
        aarch64|arm64) echo "aarch64" ;;
        *)
            log_error "Unsupported architecture: $(uname -m)"
            log_info "CloudDeskAgent supports x86_64 (Intel/AMD) and aarch64 (ARM64) architectures"
            log_info "Your architecture: $(uname -m)"
            exit 1
            ;;
    esac
}

# Distribution detection
detect_distro() {
    log_info "Detecting Linux distribution..."
    
    if [ -f /etc/os-release ]; then
        # Source the file in a subshell to avoid variable conflicts
        eval "$(grep -E '^(ID|VERSION_ID|NAME)=' /etc/os-release)"
        DISTRO=$ID
        DISTRO_VERSION=$VERSION_ID
        DISTRO_NAME=$NAME
        log_success "Detected: $DISTRO_NAME ($DISTRO $DISTRO_VERSION)"
    else
        log_error "Cannot detect Linux distribution"
        exit 1
    fi
}

# Check which dependencies need to be installed
check_missing_dependencies() {
    local missing_deps=()

    if ! command_exists wg-quick; then
        missing_deps+=("wireguard-tools")
    fi

    if ! command_exists remmina; then
        missing_deps+=("remmina")
    fi

    if ! command_exists python3; then
        missing_deps+=("python3")
    fi

    # Check for FUSE (required for AppImage)
    if ! command_exists fusermount && ! command_exists fusermount3; then
        case $DISTRO in
            ubuntu|debian|linuxmint)
                missing_deps+=("fuse" "libfuse2")
                ;;
            fedora|centos|rhel|rocky|almalinux)
                missing_deps+=("fuse" "fuse-libs")
                ;;
            arch|manjaro)
                missing_deps+=("fuse2")
                ;;
            opensuse*|sles)
                missing_deps+=("fuse" "libfuse2")
                ;;
            alpine)
                missing_deps+=("fuse")
                ;;
        esac
    fi

    # Check for tkinter (python3-tk)
    if ! python3 -c "import tkinter" 2>/dev/null; then
        case $DISTRO in
            ubuntu|debian|linuxmint)
                missing_deps+=("python3-tk")
                ;;
            fedora|centos|rhel|rocky|almalinux)
                missing_deps+=("python3-tkinter")
                ;;
            arch|manjaro)
                missing_deps+=("tk")
                ;;
            opensuse*|sles)
                missing_deps+=("python3-tk")
                ;;
            alpine)
                missing_deps+=("python3-tkinter")
                ;;
        esac
    fi



    echo "${missing_deps[@]}"
}

# Dependency installation
install_system_dependencies() {
    log_info "Checking system dependencies..."
    
    local missing_deps=($(check_missing_dependencies))
    
    if [ ${#missing_deps[@]} -eq 0 ]; then
        log_success "All system dependencies are already installed"
        return 0
    fi
    
    log_info "Missing dependencies: ${missing_deps[*]}"
    log_info "Installing missing system dependencies..."
    
    local packages_installed=false
    
    case $DISTRO in
        ubuntu|debian|linuxmint)
            if ! command_exists apt; then
                log_error "apt package manager not found"
                exit 1
            fi
            
            log_info "Updating package lists..."
            if sudo apt update; then
                log_success "Package lists updated"
            else
                log_error "Failed to update package lists"
                exit 1
            fi
            
            log_info "Installing: ${missing_deps[*]}..."
            if sudo apt install -y "${missing_deps[@]}"; then
                packages_installed=true
            fi
            ;;
            
        fedora)
            if ! command_exists dnf; then
                log_error "dnf package manager not found"
                exit 1
            fi
            
            log_info "Installing: ${missing_deps[*]}..."
            if sudo dnf install -y "${missing_deps[@]}"; then
                packages_installed=true
            fi
            ;;
            
        arch|manjaro)
            if ! command_exists pacman; then
                log_error "pacman package manager not found"
                exit 1
            fi
            
            log_info "Installing: ${missing_deps[*]}..."
            if sudo pacman -S --noconfirm "${missing_deps[@]}"; then
                packages_installed=true
            fi
            ;;
            
        opensuse*|sles)
            if ! command_exists zypper; then
                log_error "zypper package manager not found"
                exit 1
            fi
            
            log_info "Installing: ${missing_deps[*]}..."
            if sudo zypper install -y "${missing_deps[@]}"; then
                packages_installed=true
            fi
            ;;
            
        centos|rhel|rocky|almalinux)
            # Enable EPEL repository for additional packages
            log_info "Enabling EPEL repository..."
            if command_exists dnf; then
                sudo dnf install -y epel-release || log_warning "EPEL may already be installed"
                log_info "Installing: ${missing_deps[*]}..."
                if sudo dnf install -y "${missing_deps[@]}"; then
                    packages_installed=true
                fi
            elif command_exists yum; then
                sudo yum install -y epel-release || log_warning "EPEL may already be installed"
                log_info "Installing: ${missing_deps[*]}..."
                if sudo yum install -y "${missing_deps[@]}"; then
                    packages_installed=true
                fi
            else
                log_error "Neither dnf nor yum package manager found"
                exit 1
            fi
            ;;

        alpine)
            if ! command_exists apk; then
                log_error "apk package manager not found"
                exit 1
            fi

            log_info "Updating package index..."
            if sudo apk update; then
                log_success "Package index updated"
            else
                log_error "Failed to update package index"
                exit 1
            fi

            log_info "Installing: ${missing_deps[*]}..."
            if sudo apk add "${missing_deps[@]}"; then
                packages_installed=true
            fi
            ;;

        *)
            log_warning "Unsupported distribution: $DISTRO"
            log_info "Please install these packages manually: ${missing_deps[*]}"

            # Check if running in non-interactive mode (piped from curl)
            if [ ! -t 0 ]; then
                log_error "Cannot install packages automatically on unsupported distribution"
                log_info "Please install the packages manually and run the installer again"
                exit 1
            fi

            read -p "Have you installed the required packages? (y/N): " manual_install
            if [[ $manual_install == "y" || $manual_install == "Y" ]]; then
                packages_installed=true
            else
                log_error "Cannot continue without required packages"
                exit 1
            fi
            ;;
    esac
    
    if [ "$packages_installed" = true ]; then
        log_success "System dependencies installed successfully"
    else
        log_error "Failed to install system dependencies"
        exit 1
    fi
}

# Verify dependencies
verify_dependencies() {
    log_info "Verifying installed dependencies..."
    
    local missing_deps=()
    
    if ! command_exists wg-quick; then
        missing_deps+=("wireguard-tools")
    fi
    
    if ! command_exists remmina; then
        missing_deps+=("remmina")
    fi
    
    if ! command_exists python3; then
        missing_deps+=("python3")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        log_info "Please install them manually and run the installer again"
        exit 1
    fi
    
    log_success "All dependencies verified"
}

# Check if already installed
check_existing_installation() {
    local appimage_path="$INSTALL_DIR/CloudDeskAgent.AppImage"
    local desktop_file="$DESKTOP_DIR/clouddeskagent.desktop"

    if [ -f "$appimage_path" ] && [ -f "$desktop_file" ]; then
        log_warning "CloudDeskAgent appears to be already installed"

        # Check if running in non-interactive mode (piped from curl)
        if [ ! -t 0 ]; then
            log_info "Non-interactive mode detected, proceeding with reinstallation..."
            return 0
        fi

        echo
        read -p "Do you want to reinstall/update? [Y/n]: " reinstall_choice
        case $reinstall_choice in
            [Nn]*)
                log_info "Installation cancelled by user"
                exit 0
                ;;
            *)
                log_info "Proceeding with reinstallation..."
                ;;
        esac
    fi
}

# Download AppImage
download_appimage() {
    log_info "Downloading CloudDeskAgent AppImage..."

    mkdir -p "$INSTALL_DIR"

    # Set architecture-specific AppImage name
    local arch=$(get_architecture)
    APPIMAGE_NAME="CloudDeskAgent-${arch}.AppImage"

    local download_url="https://github.com/$GITHUB_REPO/releases/latest/download/$APPIMAGE_NAME"
    local target_path="$INSTALL_DIR/CloudDeskAgent.AppImage"

    log_info "Downloading $APPIMAGE_NAME for $arch architecture..."

    if command_exists wget; then
        if wget -O "$target_path" "$download_url"; then
            log_success "AppImage downloaded successfully"
        else
            log_error "Failed to download AppImage with wget"
            exit 1
        fi
    elif command_exists curl; then
        if curl -L -o "$target_path" "$download_url"; then
            log_success "AppImage downloaded successfully"
        else
            log_error "Failed to download AppImage with curl"
            exit 1
        fi
    else
        log_error "Neither wget nor curl found. Cannot download AppImage"
        exit 1
    fi

    chmod +x "$target_path"
    log_success "AppImage made executable"
}





# Set up WireGuard permissions
setup_wireguard_permissions() {
    log_info "Setting up WireGuard permissions..."

    local username=$(whoami)
    local session_dir="$HOME/.clouddeskagent/sessions/*"
    local sudoers_file="/etc/sudoers.d/clouddeskagent"
    local sudoers_line="$username ALL=(ALL) NOPASSWD: /usr/bin/wg-quick up $session_dir, /usr/bin/wg-quick down $session_dir, /usr/bin/wg-quick --help"

    if [ -f "$sudoers_file" ]; then
        log_warning "WireGuard permissions already configured"
        return 0
    fi

    log_info "Adding sudoers rule for WireGuard..."
    if echo "$sudoers_line" | sudo tee "$sudoers_file" > /dev/null; then
        sudo chmod 440 "$sudoers_file"
        log_success "WireGuard permissions configured"

        # Test the permissions
        if sudo -n wg-quick --help >/dev/null 2>&1; then
            log_success "WireGuard permissions verified"
        else
            log_warning "WireGuard permissions may not be working correctly"
        fi
    else
        log_error "Failed to configure WireGuard permissions"
        exit 1
    fi
}

# Create desktop entry
create_desktop_entry() {
    log_info "Creating desktop application entry..."

    mkdir -p "$DESKTOP_DIR"

    local desktop_file="$DESKTOP_DIR/clouddeskagent.desktop"
    local appimage_path="$INSTALL_DIR/CloudDeskAgent.AppImage"

    cat > "$desktop_file" << EOF
[Desktop Entry]
Name=CloudDeskAgent
Comment=Virtual Desktop Interface Agent
Exec=$appimage_path
Icon=network-vpn
Terminal=false
Type=Application
Categories=Network;RemoteAccess;
StartupNotify=true
StartupWMClass=CloudDeskAgent
Keywords=VPN;RDP;Remote;Desktop;VDI;
EOF

    if [ -f "$desktop_file" ]; then
        chmod +x "$desktop_file"
        log_success "Desktop entry created"

        # Update desktop database if available
        if command_exists update-desktop-database; then
            update-desktop-database "$DESKTOP_DIR" 2>/dev/null || true
        fi
    else
        log_error "Failed to create desktop entry"
        exit 1
    fi
}



# Set up auto-start
setup_autostart() {
    log_info "Configuring auto-start options..."

    # Check if running in non-interactive mode (piped from curl)
    if [ ! -t 0 ]; then
        log_info "Non-interactive mode detected, enabling auto-start by default..."
        mkdir -p "$AUTOSTART_DIR"
        cp "$DESKTOP_DIR/clouddeskagent.desktop" "$AUTOSTART_DIR/"
        log_success "Auto-start enabled"
        return 0
    fi

    echo
    echo "┌─────────────────────────────────────────────────────────────┐"
    echo "│                    Auto-Start Configuration                 │"
    echo "└─────────────────────────────────────────────────────────────┘"
    echo
    echo "CloudDeskAgent can start automatically when you log in."
    echo "This ensures the agent is always available for VDI connections."
    echo

    local autostart_choice
    while true; do
        read -p "Enable auto-start on login? [Y/n]: " autostart_choice
        case $autostart_choice in
            [Yy]*|"")
                mkdir -p "$AUTOSTART_DIR"
                cp "$DESKTOP_DIR/clouddeskagent.desktop" "$AUTOSTART_DIR/"
                log_success "Auto-start enabled"
                break
                ;;
            [Nn]*)
                log_info "Auto-start disabled (you can enable it later from the tray menu)"
                break
                ;;
            *)
                echo "Please answer yes (y) or no (n)."
                ;;
        esac
    done
}

# Launch application
launch_application() {
    log_info "Launching CloudDeskAgent..."

    local appimage_path="$INSTALL_DIR/CloudDeskAgent.AppImage"

    log_debug "AppImage path: $appimage_path"
    log_debug "AppImage exists: $([ -f "$appimage_path" ] && echo 'YES' || echo 'NO')"
    log_debug "AppImage executable: $([ -x "$appimage_path" ] && echo 'YES' || echo 'NO')"

    if [ ! -f "$appimage_path" ]; then
        log_error "CloudDeskAgent AppImage not found at: $appimage_path"
        exit 1
    fi

    if [ ! -x "$appimage_path" ]; then
        log_error "CloudDeskAgent AppImage is not executable"
        exit 1
    fi

    # Kill any existing processes first
    pkill -f "CloudDeskAgent" 2>/dev/null || true
    sleep 1

    # Try normal launch first
    log_info "Attempting normal AppImage launch..."
    local launch_output=$(mktemp)
    nohup "$appimage_path" >"$launch_output" 2>&1 &
    local launch_pid=$!
    log_debug "Launch PID: $launch_pid"
    sleep 3

    # Check if it's running
    if pgrep -f "CloudDeskAgent" >/dev/null; then
        log_success "CloudDeskAgent launched successfully"
        log_info "Look for the system tray icon to access the agent"
        log_debug "Process running with PID: $(pgrep -f CloudDeskAgent | head -1)"
        rm -f "$launch_output"
        return 0
    fi

    log_warning "Normal launch failed, trying extract-and-run mode..."
    log_debug "Launch output: $(cat "$launch_output" 2>/dev/null || echo 'No output')"

    # Kill any failed processes
    pkill -f "CloudDeskAgent" 2>/dev/null || true
    sleep 1

    # Try extract-and-run mode (works without FUSE)
    log_info "Trying extract-and-run mode (works without FUSE)..."
    nohup "$appimage_path" --appimage-extract-and-run >"$launch_output" 2>&1 &
    local extract_pid=$!
    log_debug "Extract-and-run PID: $extract_pid"
    sleep 5

    if pgrep -f "CloudDeskAgent" >/dev/null; then
        log_success "CloudDeskAgent launched in extract-and-run mode"
        log_info "Look for the system tray icon to access the agent"
        log_debug "Process running with PID: $(pgrep -f CloudDeskAgent | head -1)"
        rm -f "$launch_output"
        return 0
    fi

    # Final failure
    log_error "CloudDeskAgent failed to start in both modes"
    log_debug "Extract-and-run output: $(cat "$launch_output" 2>/dev/null || echo 'No output')"
    log_info "Manual troubleshooting steps:"
    log_info "1. Try manually: $appimage_path"
    log_info "2. Install FUSE: sudo apt install fuse libfuse2"
    log_info "3. Check system tray support for your desktop environment"
    log_info "4. View full log: $LOG_FILE"

    rm -f "$launch_output"
    exit 1
}

# Create necessary directories
create_directories() {
    log_info "Creating necessary directories..."

    mkdir -p "$INSTALL_DIR"
    mkdir -p "$DESKTOP_DIR"
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$CONFIG_DIR/sessions"

    log_success "Directories created"
}

# Main installation function
main() {
    echo
    echo "┌─────────────────────────────────────────────────────────────┐"
    echo "│                  CloudDeskAgent Installer                   │"
    echo "│                      Version $VERSION                        │"
    echo "└─────────────────────────────────────────────────────────────┘"
    echo

    # Initialize log
    log "Starting CloudDeskAgent installation"
    log "Installer version: $VERSION"

    # Log detailed system information
    log_debug "=== SYSTEM INFORMATION ==="
    log_debug "Date: $(date)"
    log_debug "User: $(whoami)"
    log_debug "Home: $HOME"
    log_debug "OS: $(uname -a)"
    log_debug "Distribution: $(cat /etc/os-release 2>/dev/null | head -5 || echo 'Unknown')"
    log_debug "Architecture: $(uname -m)"
    log_debug "Desktop Environment: ${XDG_CURRENT_DESKTOP:-Unknown}"
    log_debug "Display: ${DISPLAY:-Not set}"
    log_debug "Shell: $SHELL"
    log_debug "Python3: $(command_exists python3 && python3 --version 2>/dev/null || echo 'Not found')"
    log_debug "FUSE: $(command_exists fusermount && echo 'fusermount available' || command_exists fusermount3 && echo 'fusermount3 available' || echo 'Not found')"
    log_debug "WireGuard: $(command_exists wg && wg --version 2>/dev/null || echo 'Not found')"
    log_debug "Remmina: $(command_exists remmina && remmina --version 2>/dev/null | head -1 || echo 'Not found')"
    log_debug "=== END SYSTEM INFO ==="

    # Check if running as root
    if is_root; then
        log_error "Please do not run this installer as root"
        log_info "The installer will ask for sudo permissions when needed"
        exit 1
    fi

    # Check architecture
    local arch=$(get_architecture)
    log_info "Architecture: $arch"

    # Run installation steps
    check_existing_installation
    detect_distro
    install_system_dependencies
    verify_dependencies
    create_directories
    download_appimage
    setup_wireguard_permissions
    create_desktop_entry
    setup_autostart
    launch_application

    echo
    echo "┌─────────────────────────────────────────────────────────────┐"
    echo "│                   Installation Complete!                    │"
    echo "└─────────────────────────────────────────────────────────────┘"
    echo
    log_success "CloudDeskAgent installation completed successfully"
    echo "CloudDeskAgent has been installed and is now running!"
    echo
    echo "What's next:"
    echo "  - Look for the CloudDeskAgent icon in your system tray"
    echo "  - Find 'CloudDeskAgent' in your application menu"
    echo "  - The agent is ready to receive VDI connections"
    echo
    echo "Installation details:"
    echo "  - Application: $INSTALL_DIR/CloudDeskAgent.AppImage"
    echo "  - Log file: $LOG_FILE"
    echo "  - Auto-start: $([ -f "$AUTOSTART_DIR/clouddeskagent.desktop" ] && echo "Enabled" || echo "Disabled")"
    echo

    # Log final status for debugging
    log_debug "=== INSTALLATION SUMMARY ==="
    log_debug "Installation completed at: $(date)"
    log_debug "AppImage installed: $([ -f ~/.local/bin/CloudDeskAgent.AppImage ] && echo 'YES' || echo 'NO')"
    log_debug "Desktop entry created: $([ -f ~/.local/share/applications/CloudDeskAgent.desktop ] && echo 'YES' || echo 'NO')"
    log_debug "Autostart enabled: $([ -f ~/.config/autostart/CloudDeskAgent.desktop ] && echo 'YES' || echo 'NO')"
    log_debug "Process running: $(pgrep -f CloudDeskAgent >/dev/null && echo 'YES' || echo 'NO')"
    log_debug "Port 8765 listening: $(lsof -i :8765 >/dev/null 2>&1 && echo 'YES' || echo 'NO')"
    log_debug "API responding: $(curl -s --connect-timeout 2 http://127.0.0.1:8765/public-key >/dev/null 2>&1 && echo 'YES' || echo 'NO')"
    log_debug "Log file location: $LOG_FILE"
    log_debug "=== END SUMMARY ==="
    echo "Need help? Visit: https://github.com/$GITHUB_REPO"
    echo
}

# Run main function
main "$@"
