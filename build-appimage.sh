#!/bin/bash
# CloudDeskAgent AppImage Builder
# Creates a production-ready AppImage with embedded Python and dependencies

set -e

# Configuration
APP_NAME="CloudDeskAgent"
APP_VERSION="1.0.0"
PYTHON_VERSION="3.11"
ARCH="x86_64"
BUILD_DIR="build"
APPDIR="$BUILD_DIR/AppDir"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check dependencies
check_dependencies() {
    log_info "Checking build dependencies..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is required"
        exit 1
    fi
    
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 is required"
        exit 1
    fi
    
    log_success "Dependencies OK"
}

# Clean previous build
clean_build() {
    log_info "Cleaning previous build..."
    rm -rf "$BUILD_DIR"
    rm -f "$APP_NAME-$ARCH.AppImage"
    log_success "Build directory cleaned"
}

# Create AppDir structure
create_appdir() {
    log_info "Creating AppDir structure..."
    
    mkdir -p "$APPDIR/usr/bin"
    mkdir -p "$APPDIR/usr/lib"
    mkdir -p "$APPDIR/usr/share/applications"
    mkdir -p "$APPDIR/usr/share/icons/hicolor/256x256/apps"
    mkdir -p "$APPDIR/opt/python"
    
    log_success "AppDir structure created"
}

# Install Python and dependencies
install_python() {
    log_info "Installing Python and dependencies..."
    
    # Create virtual environment in AppDir
    python3 -m venv "$APPDIR/opt/python"
    source "$APPDIR/opt/python/bin/activate"
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install dependencies (Linux-specific)
    pip install -r requirements-linux.txt
    
    # Install application
    pip install -e .
    
    deactivate
    log_success "Python and dependencies installed"
}

# Copy application files
copy_app_files() {
    log_info "Copying application files..."
    
    # Copy Python application
    cp -r clouddeskagent "$APPDIR/opt/"
    
    # Copy assets
    cp favicon.png "$APPDIR/opt/" 2>/dev/null || true
    cp config.json "$APPDIR/opt/" 2>/dev/null || true
    
    log_success "Application files copied"
}

# Create AppRun launcher
create_apprun() {
    log_info "Creating AppRun launcher..."
    
    cat > "$APPDIR/AppRun" << 'EOF'
#!/bin/bash
# CloudDeskAgent AppImage Launcher

# Get the directory where this AppImage is mounted
APPDIR="$(dirname "$(readlink -f "${0}")")"

# Find Python version dynamically
PYTHON_VER=$(ls "${APPDIR}/opt/python/lib/" | grep python | head -1)

# Set up Python environment
export PYTHONPATH="${APPDIR}/opt/python/lib/${PYTHON_VER}/site-packages:${APPDIR}/opt:${PYTHONPATH}"
export PATH="${APPDIR}/opt/python/bin:${PATH}"

# Change to app directory
cd "${APPDIR}/opt"

# Launch the application
exec "${APPDIR}/opt/python/bin/python" -m clouddeskagent "$@"
EOF
    
    chmod +x "$APPDIR/AppRun"
    log_success "AppRun launcher created"
}

# Create desktop file
create_desktop_file() {
    log_info "Creating desktop file..."
    
    cat > "$APPDIR/$APP_NAME.desktop" << EOF
[Desktop Entry]
Name=$APP_NAME
Comment=Virtual Desktop Interface Agent
Exec=AppRun
Icon=$APP_NAME
Terminal=false
Type=Application
Categories=Network;RemoteAccess;
StartupNotify=true
StartupWMClass=$APP_NAME
Keywords=VPN;RDP;Remote;Desktop;VDI;
EOF
    
    # Copy to standard location
    cp "$APPDIR/$APP_NAME.desktop" "$APPDIR/usr/share/applications/"
    
    log_success "Desktop file created"
}

# Create icon
create_icon() {
    log_info "Creating application icon..."
    
    if [ -f "favicon.png" ]; then
        cp favicon.png "$APPDIR/usr/share/icons/hicolor/256x256/apps/$APP_NAME.png"
        cp favicon.png "$APPDIR/$APP_NAME.png"
        log_success "Icon created from favicon.png"
    else
        # Create a simple default icon
        cat > "$APPDIR/$APP_NAME.png" << 'EOF'
# This would be a base64 encoded PNG, but for now we'll use a placeholder
# In production, you'd want a proper icon file
EOF
        log_info "Using placeholder icon (add favicon.png for custom icon)"
    fi
}

# Download appimagetool
download_appimagetool() {
    log_info "Downloading appimagetool..."
    
    local tool_url="https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
    
    if [ ! -f "appimagetool-x86_64.AppImage" ]; then
        if command -v wget &> /dev/null; then
            wget -q "$tool_url"
        elif command -v curl &> /dev/null; then
            curl -L -o "appimagetool-x86_64.AppImage" "$tool_url"
        else
            log_error "Neither wget nor curl found"
            exit 1
        fi
        
        chmod +x appimagetool-x86_64.AppImage
    fi
    
    log_success "appimagetool ready"
}

# Build AppImage
build_appimage() {
    log_info "Building AppImage..."
    
    # Set environment variables for appimagetool
    export ARCH="$ARCH"
    export VERSION="$APP_VERSION"
    
    # Build the AppImage
    ./appimagetool-x86_64.AppImage "$APPDIR" "$APP_NAME-$ARCH.AppImage"
    
    if [ -f "$APP_NAME-$ARCH.AppImage" ]; then
        chmod +x "$APP_NAME-$ARCH.AppImage"
        log_success "AppImage built successfully: $APP_NAME-$ARCH.AppImage"
        
        # Show file info
        ls -lh "$APP_NAME-$ARCH.AppImage"
    else
        log_error "AppImage build failed"
        exit 1
    fi
}

# Test AppImage
test_appimage() {
    log_info "Testing AppImage..."
    
    if [ -f "$APP_NAME-$ARCH.AppImage" ]; then
        # Test that it's executable
        if ./"$APP_NAME-$ARCH.AppImage" --help &>/dev/null; then
            log_success "AppImage test passed"
        else
            log_info "AppImage created (manual testing recommended)"
        fi
    fi
}

# Main build process
main() {
    echo
    echo "CloudDeskAgent AppImage Builder"
    echo "==============================="
    echo
    
    check_dependencies
    clean_build
    create_appdir
    install_python
    copy_app_files
    create_apprun
    create_desktop_file
    create_icon
    download_appimagetool
    build_appimage
    test_appimage
    
    echo
    log_success "Build complete!"
    echo
    echo "Files created:"
    echo "  - $APP_NAME-$ARCH.AppImage ($(du -h "$APP_NAME-$ARCH.AppImage" | cut -f1))"
    echo
    echo "Upload these files to GitHub releases:"
    echo "  1. install.sh"
    echo "  2. installer.sh" 
    echo "  3. $APP_NAME-$ARCH.AppImage"
    echo
    echo "Then test with:"
    echo "  curl -sSL https://github.com/USER/REPO/releases/latest/download/install.sh | bash"
    echo
}

# Run main function
main "$@"
