#!/bin/bash
# CloudDeskAgent Quick Install Script (Two-Step Approach)
# Downloads the installer and prompts user to run it interactively

set -euo pipefail

# Configuration
# TODO: Update this URL when changing GitHub repository
# Current repository: HaSanAlkholy/agent
# Format: https://github.com/USERNAME/REPOSITORY/releases/latest/download/installer.sh
readonly INSTALLER_URL="https://github.com/HaSanAlkholy/agent/releases/latest/download/installer.sh"
readonly INSTALLER_FILE="clouddesk-installer.sh"
readonly SCRIPT_NAME="CloudDeskAgent Quick Install"

# Colors
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly BLUE='\033[0;34m'
readonly YELLOW='\033[0;33m'
readonly NC='\033[0m'

# Logging
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    log_error "Please do not run this script as root"
    log_info "The installer will ask for sudo permissions when needed"
    exit 1
fi

# Check for required tools
if ! command -v curl >/dev/null 2>&1 && ! command -v wget >/dev/null 2>&1; then
    log_error "Neither curl nor wget found"
    log_info "Please install curl or wget and try again"
    exit 1
fi

echo
echo "┌─────────────────────────────────────────────────────────────┐"
echo "│                CloudDeskAgent Quick Install                 │"
echo "└─────────────────────────────────────────────────────────────┘"
echo

log_info "Downloading CloudDeskAgent installer..."

# Download the installer
if command -v curl >/dev/null 2>&1; then
    if curl -fsSL "$INSTALLER_URL" -o "$INSTALLER_FILE"; then
        log_success "Installer downloaded successfully"
    else
        log_error "Failed to download installer"
        exit 1
    fi
elif command -v wget >/dev/null 2>&1; then
    if wget -q "$INSTALLER_URL" -O "$INSTALLER_FILE"; then
        log_success "Installer downloaded successfully"
    else
        log_error "Failed to download installer"
        exit 1
    fi
fi

# Make executable
chmod +x "$INSTALLER_FILE"

echo
echo "┌─────────────────────────────────────────────────────────────┐"
echo "│                     Ready to Install                        │"
echo "└─────────────────────────────────────────────────────────────┘"
echo

log_success "Installer ready: $INSTALLER_FILE"
echo
log_info "The installer needs sudo access to:"
echo "  • Install system dependencies (WireGuard, Remmina, Python)"
echo "  • Configure WireGuard permissions"
echo
log_warning "Run the installer now:"
echo -e "${GREEN}  ./$INSTALLER_FILE${NC}"
echo
log_info "Or run it later when convenient"
