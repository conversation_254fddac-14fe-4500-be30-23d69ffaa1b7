#!/bin/bash
# CloudDeskAgent AppImage Launcher

# Get the directory where this AppImage is mounted
APPDIR="$(dirname "$(readlink -f "${0}")")"

# Find Python version dynamically
PYTHON_VER=$(ls "${APPDIR}/opt/python/lib/" | grep python | head -1)

# Set up Python environment
export PYTHONPATH="${APPDIR}/opt/python/lib/${PYTHON_VER}/site-packages:${APPDIR}/opt:${PYTHONPATH}"
export PATH="${APPDIR}/opt/python/bin:${PATH}"

# Change to app directory
cd "${APPDIR}/opt"

# Launch the application
exec "${APPDIR}/opt/python/bin/python" -m clouddeskagent "$@"
